body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    background-color: #f8f9fa;
    height: 100vh;
    overflow: hidden;
}

.container-fluid {
    height: 100vh;
    padding: 0;
}

.row {
    height: 100%;
    margin: 0;
}

/* Sidebar styles */
.sidebar {
    background-color: #fff;
    border-right: 1px solid #e9ecef;
    padding: 20px;
    height: 100vh;
    overflow-y: auto;
}

.sidebar-header {
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
    margin-bottom: 20px;
}

.sidebar-header h2 {
    margin-bottom: 5px;
    color: #343a40;
}

.sidebar-header p {
    font-size: 0.85rem;
    line-height: 1.4;
    margin-bottom: 0;
    word-wrap: break-word;
}

.progress-section {
    margin-top: 30px;
}

/* Web search toggle styles */
.form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.form-check-input:focus {
    border-color: #86b7fe;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-check-label {
    cursor: pointer;
}

.feedback-section {
    background-color: #f8f9fa;
    border-radius: 6px;
    padding: 15px;
    border-left: 4px solid #17a2b8;
    transition: all 0.3s ease;
}

.feedback-section.active {
    border-left-color: #28a745;
    background-color: #f0f9f0;
}

.progress-item {
    margin-bottom: 15px;
    padding: 12px;
    border-radius: 6px;
    background-color: #f8f9fa;
    border-left: 4px solid #6c757d;
}

.progress-item.search {
    border-left-color: #007bff;
}

.progress-item.result {
    border-left-color: #28a745;
}

.progress-item.learning {
    border-left-color: #fd7e14;
}

.progress-item h5 {
    margin-bottom: 5px;
    font-size: 0.9rem;
    font-weight: 600;
}

.progress-item p {
    margin-bottom: 0;
    font-size: 0.85rem;
    color: #6c757d;
}

.progress-item .badge {
    font-size: 0.7rem;
    padding: 3px 6px;
}

/* Main content styles */
.main-content {
    padding: 20px;
    height: 100vh;
    overflow-y: auto;
}

.report-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
    margin-bottom: 20px;
}

#report-container {
    background-color: #fff;
    border-radius: 6px;
    padding: 25px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    min-height: calc(100vh - 100px);
}

.placeholder-message {
    display: flex;
    height: 100%;
    min-height: 400px;
    align-items: center;
    justify-content: center;
    color: #6c757d;
}

/* Markdown content styling */
.markdown-content {
    line-height: 1.6;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3 {
    margin-top: 1.5em;
    margin-bottom: 0.75em;
}

.markdown-content h1:first-child,
.markdown-content h2:first-child,
.markdown-content h3:first-child {
    margin-top: 0;
}

.markdown-content p {
    margin-bottom: 1em;
}

.markdown-content pre {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    overflow-x: auto;
}

.markdown-content code {
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
    font-size: 0.9em;
}

.markdown-content blockquote {
    border-left: 4px solid #e9ecef;
    padding-left: 15px;
    color: #6c757d;
    margin-left: 0;
}

.markdown-content table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 1em;
}

.markdown-content th,
.markdown-content td {
    border: 1px solid #e9ecef;
    padding: 8px 12px;
}

.markdown-content th {
    background-color: #f8f9fa;
}

/* Loading animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(0,0,0,0.1);
    border-radius: 50%;
    border-top-color: #007bff;
    animation: spin 1s ease-in-out infinite;
    margin-right: 10px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Editor styles */
#editor-container {
    background-color: #fff;
    border-radius: 6px;
    padding: 25px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    min-height: calc(100vh - 100px);
}

.editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.editor-title {
    font-size: 1.2rem;
    font-weight: 500;
    color: #343a40;
}

.code-editor {
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
    font-size: 0.9rem;
    line-height: 1.5;
    resize: none;
    height: calc(100vh - 180px);
    background-color: #f8f9fa;
    border-color: #e9ecef;
}

.code-editor:focus {
    background-color: #f8f9fa;
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .sidebar, .main-content {
        height: auto;
        overflow-y: visible;
    }

    .container-fluid, .row {
        height: auto;
    }

    body {
        overflow-y: auto;
    }
}

/* Knowledge Base link styles */
.knowledge-base-link {
    color: #6f42c1 !important;
    font-weight: 500;
    text-decoration: none;
    border: 1px solid #6f42c1;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.85rem;
    background-color: rgba(111, 66, 193, 0.1);
    transition: all 0.2s ease;
}

.knowledge-base-link:hover {
    background-color: #6f42c1;
    color: white !important;
    text-decoration: none;
}
